重构计划 (Refactoring Plan)
本次重构的核心目标是移除对后端的依赖，将核心AI功能前置，并实现本地化的用户配置。

第一阶段：解耦登录与认证逻辑
此阶段的目标是移除强制登录，让用户无需认证即可使用插件核心功能。

UI层修改 (packages/ui):

目标文件: packages/ui/src/components/AuthStatus.tsx
修改内容:
找到“登录”按钮的点击事件处理器。
移除或注释掉当前触发 vscode.postMessage 以启动OAuth流程的逻辑。
替换为显示一个提示信息，例如使用 vscode.postMessage 发送一个通知命令，由插件端捕获并显示 "登录功能暂未实现，请在配置中填写您的AI服务信息" 的提示。
插件扩展层修改 (packages/extension):

目标文件: packages/extension/src/services/AuthService.ts
修改内容:
修改或注释掉 getAccount 或类似的检查用户登录状态的方法。使其总是返回一个“游客”或“未登录”但允许继续操作的状态。
移除所有在调用AI功能前检查 AuthService 登录状态的前置代码。这些检查可能分布在 packages/extension/src/controllers/ActionController.ts 中。
第二阶段：前端AI能力实现
此阶段将后端AI调用逻辑迁移到插件前端。

创建新的前端AI服务:

新文件: 在 packages/extension/src/services/ 目录下创建 FrontendAIService.ts。
逻辑迁移:
参考 packages/backend/app/services/ai_proxy.py 的实现。
使用 axios 或 Node.js 内置的 https 模块，在 FrontendAIService.ts 中实现一个函数，用于向用户配置的 baseURL 发送符合OpenAI API格式的请求。
该函数需要接收 apiKey、model 和 messages 等参数。
迁移Prompts:

目标目录: 将 packages/backend/app/prompts/ 下的所有prompt（如 polish_prompts.py）中的文本内容，迁移到 packages/extension/src 下的一个新目录，例如 src/prompts/。
格式转换: 将Python字符串转换为TypeScript的常量或模板字符串。
替换服务调用:

目标文件: packages/extension/src/controllers/ActionController.ts
修改内容:
将所有对 BackendAIService 的调用，替换为对新创建的 FrontendAIService 的调用。
调用时，需要从新的配置服务中获取用户设置的 baseURL, apiKey, model。
保留 packages/extension/src/services/BackendAIService.ts 文件，但不再调用它。
第三阶段：用户配置界面与数据管理
此阶段实现让用户在插件内部配置AI服务信息。

配置数据存储:

目标文件: packages/utils/src/services/ConfigService.ts (如果不存在，则在 packages/extension/src/services/ 下创建)
修改内容:
利用VS Code的 ExtensionContext.globalState API 来存储用户的配置。这是一个键值对存储，可以持久化数据而无需写入 settings.json。
实现 getConfig() 和 saveConfig(config) 方法，用于读取和写入包含 baseURL, apiKey, model 的配置对象。
创建配置UI组件:

新文件: 在 packages/ui/src/components/ 目录下创建 ConfigProvider.tsx。
组件功能:
提供三个输入框，分别用于 基础URL (Base URL)、API密钥 (API Key) 和 模型 (Model)。
提供一个“保存”按钮。点击后，通过 vscode.postMessage 将配置数据发送到插件扩展层。
实现配置引导流程:

目标文件: packages/ui/src/App.tsx 和 packages/extension/src/SidebarProvider.ts
修改内容:
SidebarProvider.ts: 当Webview被创建时，立即从 ConfigService 读取配置。通过 webview.postMessage 将配置信息（或空状态）发送给UI。
App.tsx:
在组件加载时，监听来自插件扩展层的消息，以接收初始配置状态。
使用一个state（例如 isConfigured）来控制显示内容。
如果 isConfigured 为 false，则渲染 <ConfigProvider /> 组件。
如果 isConfigured 为 true，则渲染主聊天界面（如 <ChatWindow />）。
当用户在 <ConfigProvider /> 中点击保存后，ActionController.ts 接收消息，调用 ConfigService 保存数据，然后通知Webview更新状态，UI随之切换到主界面。
预期功能与数据流 (Expected Functionality & Data Flow)
重构完成后，插件的运行模式将发生根本性变化。

预期功能:
免登录启动: 用户安装并打开插件后，无需任何登录操作即可进入主界面或配置界面。
首次使用引导: 首次打开插件时，会显示一个配置页面，要求用户输入其AI服务的URL、API密钥和模型名称。
本地配置: 用户的AI服务配置被安全地存储在本地VS Code环境中，不会同步到云端，也不依赖任何后端服务。
前端AI调用: 所有的AI功能（如代码优化、翻译、解释等）请求都由VS Code插件直接发送到用户指定的AI服务地址。
后端停用: 项目自带的Python后端服务在整个工作流程中将不再被使用，但代码会保留以便未来参考或恢复。
数据流:
启动与配置检查 (Initialization & Config Check):

User -> 打开Docmate插件侧边栏。
Extension (SidebarProvider.ts) -> 激活并从 globalState 读取AI配置。
Extension -> UI (App.tsx): 发送消息，告知配置是否存在。
UI (App.tsx) -> 根据收到的消息，决定渲染 配置页面 或 主聊天页面。
用户配置流程 (User Configuration Flow):

User -> 在 配置页面 输入URL, API Key, Model -> 点击“保存”。
UI (ConfigProvider.tsx) -> Extension (ActionController.ts): 发送 saveConfig 消息及配置数据。
Extension (ActionController.ts) -> 调用 ConfigService.saveConfig() 将数据存入 globalState。
Extension -> UI: 发送 configUpdated 消息。
UI (App.tsx) -> 收到消息后，重新渲染，显示 主聊天页面。
AI功能调用流程 (AI Feature Invocation Flow):

User -> 在 主聊天页面 输入文本，选择功能（如“润色”） -> 点击“发送”。
UI (InputPanel.tsx) -> Extension (ActionController.ts): 发送 executeAction 消息及用户输入。
Extension (ActionController.ts) -> 调用 FrontendAIService 中的方法。
Extension (FrontendAIService.ts):
从 ConfigService 读取已保存的URL, API Key, Model。
从 src/prompts/ 加载对应的prompt模板。
构造符合OpenAI规范的HTTP请求。
直接向用户配置的 baseURL 发送请求。
User's AI Provider -> Extension (FrontendAIService.ts): 返回AI生成的文本。
Extension -> UI: 将结果发送回UI进行展示。
UI (ChatWindow.tsx) -> 接收并渲染AI的响应结果。
这个计划全面地覆盖了你提出的三点需求，并提供了具体到文件层面的实施路径，希望能帮助你顺利完成这次重构。