# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Authentication and JWT
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
passlib[bcrypt]==1.7.4

# HTTP client for external API calls
httpx==0.25.2
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0
pydantic-settings==2.0.3

# CORS support (FastAPI内置，无需额外安装)

# Logging and monitoring
structlog==23.2.0

# Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Code quality (optional)
black==23.11.0
flake8==6.1.0
isort==5.12.0
