{"name": "@docmate/ui", "version": "0.1.0", "description": "React UI components for DocMate", "type": "module", "scripts": {"dev": "vite build --watch", "build": "tsc && vite build", "watch": "vite build --watch", "preview": "vite preview", "clean": "rimraf dist *.tsbuildinfo", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@docmate/shared": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0", "@vscode/webview-ui-toolkit": "^1.4.0"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/node": "^20.10.0", "@vitejs/plugin-react": "^4.2.1", "vite": "^6.0.0", "rimraf": "^5.0.5", "typescript": "^5.3.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-jsx-a11y": "^6.8.0"}, "files": ["dist/**/*"]}