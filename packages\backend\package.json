{"name": "@docmate/backend", "version": "1.0.0", "private": true, "description": "DocMate backend service with openEuler authentication", "scripts": {"dev": "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000", "start": "uvicorn app.main:app --host 0.0.0.0 --port 8000", "install": "pip install -r requirements.txt", "test": "pytest", "lint": "flake8 app/", "format": "black app/"}, "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "authentication", "ai-proxy"], "author": "openEuler DocMate Team", "license": "MIT"}