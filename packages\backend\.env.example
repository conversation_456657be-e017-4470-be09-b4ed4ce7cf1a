# 服务配置
HOST=0.0.0.0
PORT=8000
DEBUG=true

# JWT配置 (必填)
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=1440

# openEuler认证配置 (可选，大部分配置已写死在代码中)
OPENEULER_LOGIN_URL=https://id.openeuler.org/login
OPENEULER_USER_INFO_URL=https://id.openeuler.org/api/user/info

# AI服务配置 (必填)
AI_API_KEY=your-ai-api-key-here
AI_BASE_URL=https://api.openai.com/v1
AI_MODEL=gpt-3.5-turbo
AI_TIMEOUT=30
AI_MAX_RETRIES=3

# CORS配置
ALLOWED_ORIGINS=vscode-webview://*,https://localhost:*,http://localhost:*

# 日志配置
LOG_LEVEL=INFO
