{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "composite": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@docmate/shared/*": ["../shared/src/*"]}}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}